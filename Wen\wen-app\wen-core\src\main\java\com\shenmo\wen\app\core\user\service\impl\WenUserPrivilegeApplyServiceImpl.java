package com.shenmo.wen.app.core.user.service.impl;

import com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties;
import com.shenmo.wen.app.core.user.constant.PrivilegeApplyConstant;
import com.shenmo.wen.app.core.user.enums.PrivilegeApplyStatus;
import com.shenmo.wen.app.core.user.enums.PrivilegeApplyStep;
import com.shenmo.wen.app.core.user.enums.PrivilegeApplyType;
import com.shenmo.wen.app.core.user.exception.UserException;
import com.shenmo.wen.app.core.user.exception.UserExceptionEnum;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeMapper;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeTemplateMapper;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeApplyMapper;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilege;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeApply;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeApplyStartReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeApplySubmitReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenApplyTimeInfoResp;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeApplyResp;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeApplyService;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeApplyEmailService;
import com.shenmo.wen.common.constant.BucketConstant;
import com.shenmo.wen.common.objectstorage.template.ObjectStorageTemplate;
import com.shenmo.wen.common.util.EnCodingUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户特权申请服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenUserPrivilegeApplyServiceImpl implements WenUserPrivilegeApplyService {

    private final WenUserPrivilegeApplyMapper applyMapper;
    private final WenUserPrivilegeMapper privilegeMapper;
    private final WenUserPrivilegeTemplateMapper templateMapper;
    private final WenUserMapper userMapper;
    private final PrivilegeApplyProperties properties;
    private final ObjectStorageTemplate objectStorageTemplate;
    private final WenUserPrivilegeApplyEmailService emailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WenUserPrivilegeApplyResp startApply(WenUserPrivilegeApplyStartReq req) {
        final Long loginId = StpUtil.getLoginIdAsLong();
        final Long privilegeId = req.getPrivilegeId();

        // 验证特权是否存在
        final WenUserPrivilege privilege = privilegeMapper.selectById(privilegeId);
        if (privilege == null) {
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_TYPE_NOT_EXISTS);
        }

        // 检查今日是否已申请过
        if (applyMapper.existsTodayApplication(loginId, privilegeId)) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_DAILY_LIMIT_EXCEEDED);
        }
        final Integer applyType = privilege.getApplyType();
        // 验证申请类型是否匹配特权类型
        if (!applyType.equals(privilege.getApplyType())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 当申请类型为二维码时，验证qrcc字段
        if (applyType == 1 && (req.getQrcc() == null || req.getQrcc().trim().isEmpty())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 创建申请流程
        final WenUserPrivilegeApply apply = createApplyRecord(loginId, privilegeId, applyType);

        // 执行申请准备逻辑
        executePrepareApplication(apply, privilege, req.getQrcc());

        return toResp(apply);
    }

    /**
     * 创建申请流程记录
     */
    private WenUserPrivilegeApply createApplyRecord(Long userId, Long privilegeId, Integer applyType) {
        final WenUserPrivilegeApply apply = new WenUserPrivilegeApply();
        apply.setUserId(userId);
        apply.setPrivilegeId(privilegeId);
        apply.setCurrentStep(PrivilegeApplyStep.PREPARE_APPLICATION.getCode());
        apply.setStatus(PrivilegeApplyStatus.IN_PROGRESS.getCode());

        // 设置过期时间
        long expireTime = System.currentTimeMillis() + properties.getExpireMinutes() * 60 * 1000L;
        apply.setExpireTime(expireTime);

        applyMapper.insert(apply);
        return apply;
    }

    /**
     * 执行申请准备逻辑
     */
    private void executePrepareApplication(WenUserPrivilegeApply apply, WenUserPrivilege privilege, String qrcc) {
        // 获取特权模板
        final WenUserPrivilegeTemplate template = templateMapper.selectById(privilege.getTemplateId());
        if (template == null) {
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_TYPE_NOT_EXISTS);
        }

        // 获取用户信息
        final WenUser applicant = userMapper.selectById(apply.getUserId());
        // 通过特权实体获取特权提供者
        final WenUser provider = userMapper.selectById(privilege.getUserId());

        if (applicant == null || provider == null) {
            throw new UserException(UserExceptionEnum.USER_NOT_EXISTS);
        }

        // 生成申请页面并发送邮件
        generateApplyPageAndSendEmail(apply, template, applicant, provider, qrcc);

        // 更新到发起申请步骤
        apply.setCurrentStep(PrivilegeApplyStep.INITIATE_APPLICATION.getCode());
        applyMapper.updateById(apply);

        log.info("启动特权申请流程，申请ID: {}, 申请者: {}, 特权: {}", apply.getId(), applicant.getUsername(), template.getName());
    }

    /**
     * 生成申请页面并发送邮件
     */
    private void generateApplyPageAndSendEmail(WenUserPrivilegeApply apply,
            WenUserPrivilegeTemplate template, WenUser applicant, WenUser provider, String qrcc) {

        // 生成申请页面
        String applyPageUrl = generateApplyPage(apply, template, applicant, provider, qrcc);
        apply.setPageUrl(applyPageUrl);

        // 设置Redis过期监听
        String redisKey = PrivilegeApplyConstant.REDIS_KEY_PREFIX + extractMinioPath(applyPageUrl);
        SpringRedisUtils.set(redisKey, apply.getId().toString(),
                properties.getExpireMinutes(), TimeUnit.MINUTES);

        // 发送邮件给特权提供者
        sendApplyEmail(apply, template, applicant, provider, applyPageUrl);
    }

    /**
     * 生成申请页面
     */
    private String generateApplyPage(WenUserPrivilegeApply apply,
            WenUserPrivilegeTemplate template, WenUser applicant, WenUser provider, String qrcc) {

        // 获取特权类型
        final WenUserPrivilege privilege = privilegeMapper.selectById(apply.getPrivilegeId());
        PrivilegeApplyType type = PrivilegeApplyType.fromCode(privilege.getApplyType());
        String htmlContent;

        if (type == PrivilegeApplyType.SMS) {
            htmlContent = generateSmsApplyPage(apply, template, applicant, provider);
        } else {
            htmlContent = generateQrCodeApplyPage(apply, template, applicant, provider, qrcc);
        }

        // 上传到MinIO
        try {
            // 生成文件名（使用hash）
            byte[] contentBytes = htmlContent.getBytes("UTF-8");
            String hash = EnCodingUtils.calculateHash(new ByteArrayInputStream(contentBytes));
            String object = "apply/" + hash;
            objectStorageTemplate.putObject(
                    BucketConstant.PRIVILEGE,
                    object,
                    new ByteArrayInputStream(contentBytes),
                    contentBytes.length,
                    MediaType.TEXT_HTML_VALUE);

            // 生成访问URL
            return objectStorageTemplate.getPresignedObjectUrl(
                    BucketConstant.PRIVILEGE,
                    object,
                    properties.getExpireMinutes() * 60 // 转换为秒
            );

        } catch (Exception e) {
            log.error("生成申请页面失败", e);
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }
    }

    /**
     * 转换为响应对象
     */
    private WenUserPrivilegeApplyResp toResp(WenUserPrivilegeApply apply) {
        WenUserPrivilegeApplyResp resp = new WenUserPrivilegeApplyResp();
        resp.setId(apply.getId());
        resp.setUserId(apply.getUserId());
        resp.setPrivilegeId(apply.getPrivilegeId());
        resp.setCurrentStep(apply.getCurrentStep());
        resp.setStatus(apply.getStatus());
        resp.setPageUrl(apply.getPageUrl());
        resp.setExpireTime(apply.getExpireTime());
        resp.setCtTm(apply.getCtTm());

        // 获取特权名称
        final WenUserPrivilege privilege = privilegeMapper.selectById(apply.getPrivilegeId());
        if (privilege != null) {
            final WenUserPrivilegeTemplate template = templateMapper.selectById(privilege.getTemplateId());
            if (template != null) {
                resp.setPrivilegeName(template.getName());
            }
        }

        resp.setApplyType(privilege != null ? privilege.getApplyType() : null);
        return resp;
    }

    /**
     * 发送申请邮件
     */
    private void sendApplyEmail(WenUserPrivilegeApply apply,
            WenUserPrivilegeTemplate template, WenUser applicant, WenUser provider, String applyPageUrl) {

        // 获取特权类型
        final WenUserPrivilege privilege = privilegeMapper.selectById(apply.getPrivilegeId());
        PrivilegeApplyType type = PrivilegeApplyType.fromCode(privilege.getApplyType());

        boolean success = false;
        if (type == PrivilegeApplyType.SMS) {
            success = emailService.sendSmsApplyEmail(applicant, provider, template, applyPageUrl);
        } else if (type == PrivilegeApplyType.QR_CODE) {
            success = emailService.sendQrCodeApplyEmail(applicant, provider, template, applyPageUrl);
        }

        if (success) {
            log.info("申请邮件发送成功，收件人: {}, 特权: {}", provider.getEmail(), template.getName());
        } else {
            log.error("申请邮件发送失败，收件人: {}, 特权: {}", provider.getEmail(), template.getName());
        }
    }

    @Override
    public WenUserPrivilegeApplyResp applyStatus(Long applyId) {
        final WenUserPrivilegeApply apply = applyMapper.byId(applyId);
        if (apply == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查是否过期
        if (apply.getExpireTime() < System.currentTimeMillis()) {
            apply.setStatus(PrivilegeApplyStatus.TIMEOUT.getCode());
            applyMapper.updateById(apply);
        }

        return toResp(apply);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitApplyContent(Long id, WenUserPrivilegeApplySubmitReq req) {
        final WenUserPrivilegeApply apply = applyMapper.byId(id);
        if (apply == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查状态
        if (!apply.getStatus().equals(PrivilegeApplyStatus.IN_PROGRESS.getCode())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 检查步骤
        if (!apply.getCurrentStep().equals(PrivilegeApplyStep.INITIATE_APPLICATION.getCode())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 验证内容
        if (!validateApplyContent(apply, req.getContent())) {
            throw new UserException(UserExceptionEnum.QR_CODE_CONTENT_INVALID);
        }

        // 保存申请内容
        apply.setContent(req.getContent());
        applyMapper.updateById(apply);

        log.info("提交申请内容，申请ID: {}, 内容: {}", id, req.getContent());
        return true;
    }

    /**
     * 验证申请内容
     */
    private boolean validateApplyContent(WenUserPrivilegeApply apply, String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }

        final WenUserPrivilege privilege = privilegeMapper.selectById(apply.getPrivilegeId());
        if (privilege == null) {
            return false;
        }

        PrivilegeApplyType type = PrivilegeApplyType.fromCode(privilege.getApplyType());

        if (type == PrivilegeApplyType.SMS) {
            // 短信验证码验证（6位数字）
            return content.matches("\\d{6}");
        } else if (type == PrivilegeApplyType.QR_CODE) {
            // 二维码内容验证
            final WenUserPrivilegeTemplate template = templateMapper.selectById(privilege.getTemplateId());
            if (template == null || !StringUtils.hasText(template.getQrCodeUrl())) {
                return false;
            }
            // 检查二维码内容是否以模板的qrCodeUrl开头
            return content.startsWith(template.getQrCodeUrl());
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean triggerAwaitApproval(Long applyId) {
        final WenUserPrivilegeApply apply = applyMapper.byId(applyId);
        if (apply == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查状态
        if (!apply.getStatus().equals(PrivilegeApplyStatus.IN_PROGRESS.getCode())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 更新到等待通过步骤
        apply.setCurrentStep(PrivilegeApplyStep.AWAIT_APPROVAL.getCode());
        applyMapper.updateById(apply);

        log.info("触发等待通过阶段，申请流程ID: {}", applyId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiredApplies() {
        List<WenUserPrivilegeApply> expiredList = applyMapper.listExpired();
        for (WenUserPrivilegeApply apply : expiredList) {
            apply.setStatus(PrivilegeApplyStatus.TIMEOUT.getCode());
            applyMapper.updateById(apply);

            // 清理MinIO资源
            if (StringUtils.hasText(apply.getPageUrl())) {
                String minioPath = extractMinioPath(apply.getPageUrl());
                cleanupApplyPage(minioPath);
            }
        }
        log.info("处理过期申请流程数量: {}", expiredList.size());
    }

    @Override
    public void cleanupApplyPage(String minioPath) {
        try {
            objectStorageTemplate.removeObject(BucketConstant.PRIVILEGE, minioPath);
            log.info("清理申请页面资源成功: {}", minioPath);
        } catch (Exception e) {
            log.error("清理申请页面资源失败: {}", minioPath, e);
        }
    }

    /**
     * 从URL中提取MinIO路径
     */
    private String extractMinioPath(String url) {
        if (!StringUtils.hasText(url)) {
            return "";
        }
        // 从预签名URL中提取对象路径
        int index = url.indexOf("apply/");
        if (index != -1) {
            String path = url.substring(index);
            int queryIndex = path.indexOf("?");
            if (queryIndex != -1) {
                path = path.substring(0, queryIndex);
            }
            return path;
        }
        return "";
    }

    @Override
    public WenApplyTimeInfoResp startApplyTimer(Long applyId) {
        WenUserPrivilegeApply apply = applyMapper.byId(applyId);

        if (apply == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查是否已经开始计时
        if (apply.getPageAccessTime() != null) {
            // 已经开始计时，返回当前状态
            return remainingTime(applyId);
        }

        // 开始计时
        long currentTime = System.currentTimeMillis();
        apply.setPageAccessTime(currentTime);
        apply.setAutoCompleteTime(currentTime + 30000); // 30秒后自动完成
        applyMapper.updateById(apply);

        log.info("启动申请计时器: applyId={}, startTime={}", applyId, currentTime);

        WenApplyTimeInfoResp timeResp = new WenApplyTimeInfoResp();
        timeResp.setRemainingSeconds(30L);
        timeResp.setAutoCompleted(false);
        timeResp.setStartTime(currentTime);
        timeResp.setAutoCompleteTime(currentTime + 30000);
        timeResp.setStatus(apply.getStatus().toString());
        timeResp.setTimerStarted(true);

        return timeResp;
    }

    @Override
    public WenApplyTimeInfoResp remainingTime(Long applyId) {
        WenUserPrivilegeApply apply = applyMapper.byId(applyId);

        if (apply == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 如果还没有开始计时
        if (apply.getPageAccessTime() == null) {
            return startApplyTimer(applyId);
        }

        long currentTime = System.currentTimeMillis();
        long autoCompleteTime = apply.getAutoCompleteTime();

        WenApplyTimeInfoResp timeResp = new WenApplyTimeInfoResp();
        timeResp.setStartTime(apply.getPageAccessTime());
        timeResp.setAutoCompleteTime(autoCompleteTime);
        timeResp.setStatus(apply.getStatus().toString());
        timeResp.setTimerStarted(true);

        if (currentTime >= autoCompleteTime) {
            // 已经超时，自动完成
            timeResp.setRemainingSeconds(0L);
            timeResp.setAutoCompleted(true);

            // 更新申请状态为成功
            if (apply.getStatus().equals(PrivilegeApplyStatus.IN_PROGRESS.getCode())) {
                apply.setStatus(PrivilegeApplyStatus.SUCCESS.getCode());
                applyMapper.updateById(apply);
                timeResp.setStatus(PrivilegeApplyStatus.SUCCESS.getCode().toString());
            }
        } else {
            // 计算剩余时间
            long remainingMs = autoCompleteTime - currentTime;
            timeResp.setRemainingSeconds(remainingMs / 1000);
            timeResp.setAutoCompleted(false);
        }

        return timeResp;
    }

    /**
     * 生成短信申请页面
     */
    private String generateSmsApplyPage(WenUserPrivilegeApply apply,
            WenUserPrivilegeTemplate template, WenUser applicant, WenUser provider) {
        // 这里应该生成HTML页面内容，简化处理
        return String.format(
            "<html><body><h1>短信申请页面</h1><p>申请者 %s 申请特权 %s</p><p>申请ID: %d</p></body></html>",
            applicant.getUsername(), template.getName(), apply.getId()
        );
    }

    /**
     * 生成二维码申请页面
     */
    private String generateQrCodeApplyPage(WenUserPrivilegeApply apply,
            WenUserPrivilegeTemplate template, WenUser applicant, WenUser provider, String qrcc) {
        // 这里应该生成HTML页面内容，简化处理
        return String.format(
            "<html><body><h1>二维码申请页面</h1><p>申请者 %s 申请特权 %s</p><p>申请ID: %d</p><p>二维码内容: %s</p></body></html>",
            applicant.getUsername(), template.getName(), apply.getId(), qrcc
        );
    }
}
