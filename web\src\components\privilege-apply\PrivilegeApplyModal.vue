<template>
  <NModal
    v-model:show="visible"
    preset="dialog"
    title="特权申请"
    style="width: 600px"
    :closable="false"
    :mask-closable="false"
  >
    <div class="privilege-apply-modal">
      <!-- 申请步骤 -->
      <NSteps
        :current="currentStepIndex"
        :status="getStepStatus()"
        class="mb-6"
      >
        <NStep
          v-for="step in steps"
          :key="step.value"
          :title="step.title"
          :description="step.description"
        />
      </NSteps>

      <!-- 申请内容 -->
      <div class="apply-content">
        <!-- 步骤一：准备申请信息 -->
        <div v-if="apply.currentStep.value === 1" class="step-content">
          <NAlert
            title="准备申请信息"
            type="info"
            show-icon
            class="mb-4"
          >
            正在生成申请页面并发送邮件给特权提供者...
          </NAlert>
          <NSpin :show="apply.isLoading.value" />
        </div>

        <!-- 步骤二：发送申请邮件 -->
        <div v-else-if="apply.currentStep.value === 2" class="step-content">
          <NAlert
            title="等待申请确认"
            type="warning"
            show-icon
            class="mb-4"
          >
            申请邮件已发送，请等待特权提供者确认...
          </NAlert>

          <!-- 申请页面链接 -->
          <div v-if="apply.applyData.value?.pageUrl" class="mb-4">
            <NText strong>申请页面：</NText>
            <NA
              :href="apply.applyData.value.pageUrl"
              target="_blank"
              class="ml-2"
            >
              点击访问申请页面
            </NA>
          </div>

          <!-- 倒计时显示 -->
          <div v-if="apply.remainingTime.value > 0" class="countdown">
            <NStatistic
              label="剩余时间"
              :value="apply.remainingTime.value"
              suffix="秒"
            />
          </div>
        </div>

        <!-- 步骤三：等待完成 -->
        <div v-else-if="apply.currentStep.value === 3" class="step-content">
          <NAlert
            title="申请处理中"
            type="info"
            show-icon
            class="mb-4"
          >
            特权提供者正在处理您的申请，请耐心等待...
          </NAlert>
        </div>

        <!-- 申请完成 -->
        <div v-if="apply.isCompleted.value" class="step-content">
          <NResult
            status="success"
            title="申请成功！"
            description="恭喜您，特权申请已通过，您现在可以使用该特权了。"
          />
        </div>

        <!-- 申请失败 -->
        <div v-if="apply.isFailed.value" class="step-content">
          <NResult
            status="error"
            title="申请失败"
            description="很抱歉，您的特权申请未通过，请稍后重试。"
          />
        </div>

        <!-- 申请超时 -->
        <div v-if="apply.isTimeout.value" class="step-content">
          <NResult
            status="warning"
            title="申请超时"
            description="申请流程已超时，请重新发起申请。"
          />
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="modal-footer mt-6">
        <NSpace>
          <NButton
            v-if="!apply.isInProgress.value"
            @click="handleClose"
          >
            关闭
          </NButton>
          <NButton
            v-if="apply.isInProgress.value"
            @click="handleCancel"
          >
            取消申请
          </NButton>
        </NSpace>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import {
  NModal,
  NSteps,
  NStep,
  NAlert,
  NSpin,
  NText,
  NA,
  NStatistic,
  NResult,
  NSpace,
  NButton
} from 'naive-ui'
import { computed, watch, onUnmounted } from 'vue'
import { usePrivilegeApply } from '@/composables/privilege-apply/usePrivilegeApply'
import { APPLY_STEP_LABELS, APPLY_STEP_DESCRIPTIONS } from '@/constants/privilege-apply/apply-step.constants'
import type { PrivilegeApplyStartRequest } from '@/types/privilege-apply/privilege-apply-start-request.types'

// Props
interface Props {
  modelValue: boolean
  applyRequest?: PrivilegeApplyStartRequest
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 使用申请相关的组合式函数
const apply = usePrivilegeApply()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 当前步骤索引（用于步骤条显示）
const currentStepIndex = computed(() => {
  if (apply.currentStep.value) {
    return apply.currentStep.value - 1
  }
  return 0
})

// 步骤配置
const steps = [
  {
    value: 1,
    title: APPLY_STEP_LABELS[1],
    description: APPLY_STEP_DESCRIPTIONS[1],
  },
  {
    value: 2,
    title: APPLY_STEP_LABELS[2],
    description: APPLY_STEP_DESCRIPTIONS[2],
  },
  {
    value: 3,
    title: APPLY_STEP_LABELS[3],
    description: APPLY_STEP_DESCRIPTIONS[3],
  },
]

/**
 * 获取步骤状态
 */
const getStepStatus = () => {
  if (apply.isCompleted.value) return 'finish'
  if (apply.isFailed.value || apply.isTimeout.value) return 'error'
  if (apply.isInProgress.value) return 'process'
  return 'wait'
}

/**
 * 处理关闭
 */
const handleClose = () => {
  visible.value = false
  apply.reset()
}

/**
 * 处理取消申请
 */
const handleCancel = () => {
  visible.value = false
  apply.reset()
  emit('cancel')
}

// 监听申请请求变化，自动启动申请
watch(
  () => props.applyRequest,
  async (newRequest) => {
    if (newRequest && props.modelValue) {
      const success = await apply.startApply(newRequest)
      if (success) {
        // 申请启动成功，可以在这里做一些额外处理
      }
    }
  },
  { immediate: true }
)

// 监听申请完成状态
watch(
  () => apply.isCompleted.value,
  (isCompleted) => {
    if (isCompleted) {
      emit('success')
    }
  }
)

// 组件卸载时清理
onUnmounted(() => {
  apply.stopTimer()
})
</script>

<style scoped>
.privilege-apply-modal {
  .step-content {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .countdown {
    text-align: center;
    margin-top: 20px;
  }

  .modal-footer {
    text-align: center;
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
  }
}
</style>
