import { ref, computed } from 'vue'

import applyApi from '@/api/privilege-apply'
import type { ApplyTimeInfoResponse } from '@/types/privilege-apply/apply-time-info-response.types'
import type { PrivilegeApplyResponse } from '@/types/privilege-apply/privilege-apply-response.types'
import type { PrivilegeApplyStartRequest } from '@/types/privilege-apply/privilege-apply-start-request.types'
import type { PrivilegeApplySubmitRequest } from '@/types/privilege-apply/privilege-apply-submit-request.types'
import message from '@/utils/ui/message'

/**
 * 特权申请相关的组合式函数
 * 提供申请流程的状态管理和操作方法
 */
export function usePrivilegeApply() {
  // 响应式状态
  const isLoading = ref(false)
  const applyData = ref<PrivilegeApplyResponse | null>(null)
  const timeInfo = ref<ApplyTimeInfoResponse | null>(null)
  const timer = ref<number | null>(null)

  // 计算属性
  const isInProgress = computed(() => applyData.value?.status === 0)
  const isCompleted = computed(() => applyData.value?.status === 1)
  const isFailed = computed(() => applyData.value?.status === 2)
  const isTimeout = computed(() => applyData.value?.status === 3)
  const isExpired = computed(() => {
    if (!applyData.value) return false
    return Date.now() > applyData.value.expireTime
  })

  const currentStep = computed(() => applyData.value?.currentStep || 1)
  const remainingTime = computed(() => timeInfo.value?.remainingSeconds || 0)

  /**
   * 启动特权申请流程
   */
  const startApply = async (request: PrivilegeApplyStartRequest): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await applyApi.startApply(request)

      if (response.success && response.data) {
        applyData.value = response.data
        message.success('申请流程已启动')

        // 启动计时器
        await startTimer()
        return true
      } else {
        message.error(response.message || '启动申请流程失败')
        return false
      }
    } catch (error) {
      console.error('启动申请流程失败:', error)
      message.error('启动申请流程失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 查询申请流程状态
   */
  const queryApplyStatus = async (id: number): Promise<boolean> => {
    try {
      const response = await applyApi.applyStatus(id)

      if (response.success && response.data) {
        applyData.value = response.data
        return true
      } else {
        message.error(response.message || '查询申请状态失败')
        return false
      }
    } catch (error) {
      console.error('查询申请状态失败:', error)
      message.error('查询申请状态失败')
      return false
    }
  }

  /**
   * 提交申请内容
   */
  const submitApplyContent = async (
    id: number,
    request: PrivilegeApplySubmitRequest,
  ): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await applyApi.submitApplyContent(id, request)

      if (response.success && response.data) {
        message.success('申请内容提交成功')
        // 重新查询状态
        await queryApplyStatus(id)
        return true
      } else {
        message.error(response.message || '提交申请内容失败')
        return false
      }
    } catch (error) {
      console.error('提交申请内容失败:', error)
      message.error('提交申请内容失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 触发等待通过阶段
   */
  const triggerStepThree = async (id: number): Promise<boolean> => {
    try {
      const response = await applyApi.triggerStepThree(id)

      if (response.success && response.data) {
        // 重新查询状态
        await queryApplyStatus(id)
        return true
      } else {
        message.error(response.message || '触发步骤三失败')
        return false
      }
    } catch (error) {
      console.error('触发步骤三失败:', error)
      message.error('触发步骤三失败')
      return false
    }
  }

  /**
   * 启动计时器
   */
  const startTimer = async (): Promise<void> => {
    if (!applyData.value) return

    try {
      const response = await applyApi.startApplyTimer(applyData.value.id)
      if (response.success && response.data) {
        timeInfo.value = response.data
        startCountdown()
      }
    } catch (error) {
      console.error('启动计时器失败:', error)
    }
  }

  /**
   * 获取剩余时间
   */
  const getRemainingTime = async (): Promise<void> => {
    if (!applyData.value) return

    try {
      const response = await applyApi.remainingTime(applyData.value.id)
      if (response.success && response.data) {
        timeInfo.value = response.data
      }
    } catch (error) {
      console.error('获取剩余时间失败:', error)
    }
  }

  /**
   * 启动倒计时
   */
  const startCountdown = (): void => {
    if (timer.value) {
      clearInterval(timer.value)
    }

    timer.value = window.setInterval(async () => {
      if (!applyData.value) {
        return
      }

      // 更新剩余时间
      await getRemainingTime()

      // 检查是否过期
      if (isExpired.value) {
        window.clearInterval(timer.value!)
        timer.value = null
        message.warning('申请流程已过期')
        return
      }

      // 定期查询申请状态
      await queryApplyStatus(applyData.value.id)

      // 如果申请完成，停止计时器
      if (!isInProgress.value) {
        window.clearInterval(timer.value!)
        timer.value = null
      }
    }, 1000)
  }

  /**
   * 停止计时器
   */
  const stopTimer = (): void => {
    if (timer.value) {
      window.clearInterval(timer.value)
      timer.value = null
    }
  }

  /**
   * 重置状态
   */
  const reset = (): void => {
    stopTimer()
    applyData.value = null
    timeInfo.value = null
    isLoading.value = false
  }

  return {
    // 状态
    isLoading,
    applyData,
    timeInfo,

    // 计算属性
    isInProgress,
    isCompleted,
    isFailed,
    isTimeout,
    isExpired,
    currentStep,
    remainingTime,

    // 方法
    startApply,
    queryApplyStatus,
    submitApplyContent,
    triggerStepThree,
    startTimer,
    getRemainingTime,
    startCountdown,
    stopTimer,
    reset,
  }
}
